from __future__ import annotations
from caas.commands import BashScript, Exec, UploadFile
from caas.protocol import NetworkMode, VolumeMount
from caas_tool.caas_container import CaasContainer


async def main():
    container = await CaasContainer.new(
        image_name="aio",
        caas_endpoint="https://southcentralus.caas.azure.com",
        idle_ttl=1200,
        memory_limit="4g",
        network=NetworkMode.CAAS_PUBLIC_ONLY,
    )
    print("CaaS container ready ✅")
    session = container.terminal_session.session

    # Upload mopub to the container
    print("Uploading mopub...")
    await session.run(
        Exec(
            ["bash", "-lc", "mkdir -p /usr/local/bin/mopub"],
            timeout=60,
            workdir="/",
            env=None,
        )
    )

    await session.run(UploadFile("/usr/local/bin/mopub/webhook.py", open("/Users/<USER>/code/glass/personal/lixiaoli/mopub/webhook.py", "rb").read()))
    print("mopub uploaded ✅")
